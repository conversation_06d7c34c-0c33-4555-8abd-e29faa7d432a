// Mock数据用于申诉详情页面测试
export const mockAppealDetail: API.AppealDetail = {
    id: '1',
    appealNo: 'AP202312150001',
    licensePlateNumber: '粤A88888',
    userPhone: '138****8888',
    merchantId: 'M001',
    appealType: '01',
    appealTypeName: '停车费',
    appealStatus: '03',
    appealStatusName: '待商家处理',
    priority: '02',
    priorityName: '中',
    appealTitle: '停车费收费异常申诉',
    appealContent:
        '我在贵停车场停车2小时，但是被收取了全天费用，明显不合理。我有停车小票和支付记录作为证据，希望能够退还多收的费用。',
    appealAmount: 50.0,
    orderNo: 'ORD202312150001',
    stationId: 'ST001',
    stationName: '万达广场停车场',
    operatorName: '万达物业',
    cityName: '北京市',
    appealTime: '2023-12-15 10:30:00',
    handleTime: '',
    handlePerson: '',
    handleResult: '',
    handleRemark: '',
    deadlineTime: '2023-12-17 10:30:00',
    createTime: '2023-12-15 10:30:00',
    updateTime: '2023-12-15 10:30:00',
    attachments: [
        {
            id: '1',
            fileName: '停车小票.jpg',
            fileUrl: 'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=停车小票',
            fileType: 'image/jpeg',
            fileSize: 1024000,
            uploadTime: '2023-12-15 10:32:00',
        },
        {
            id: '2',
            fileName: '支付记录.jpg',
            fileUrl: 'https://via.placeholder.com/300x200/2196F3/FFFFFF?text=支付记录',
            fileType: 'image/jpeg',
            fileSize: 856000,
            uploadTime: '2023-12-15 10:33:00',
        },
    ],
    handleHistory: [
        {
            id: '1',
            appealNo: 'AP202312150001',
            licensePlateNumber: '粤A88888',
            operationType: '01',
            operationTypeName: '用户提交申诉',
            operationContent: '用户提交停车费申诉，申诉金额50元',
            operatorId: 'U001',
            operatorName: '张三',
            operationTime: '2023-12-15 10:30:00',
            remark: '申诉已提交，等待处理',
        },
        {
            id: '2',
            appealNo: 'AP202312150001',
            licensePlateNumber: '粤A88888',
            operationType: '02',
            operationTypeName: '系统自动分配',
            operationContent: '申诉已自动分配给商家处理',
            operatorId: 'SYS',
            operatorName: '系统',
            operationTime: '2023-12-15 10:31:00',
            remark: '自动分配',
        },
    ],
    replyHistory: [],
};

// 已处理的申诉mock数据
export const mockProcessedAppealDetail: API.AppealDetail = {
    ...mockAppealDetail,
    appealNo: 'AP202312140001',
    appealStatus: '05',
    appealStatusName: '商家通过',
    handleTime: '2023-12-15 14:30:00',
    handlePerson: '李四',
    handleResult: '申诉成功',
    handleRemark: '经核实，确实存在收费异常，已安排退款处理',
    handleHistory: [
        ...mockAppealDetail.handleHistory!,
        {
            id: '3',
            appealNo: 'AP202312140001',
            licensePlateNumber: '粤A88888',
            operationType: '03',
            operationTypeName: '商家处理',
            operationContent: '商家同意申诉，退款50元',
            operatorId: 'M001',
            operatorName: '李四',
            operationTime: '2023-12-15 14:30:00',
            remark: '已退款',
        },
    ],
};
