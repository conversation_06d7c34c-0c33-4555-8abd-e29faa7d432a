import React, { useState } from 'react';
import { Card, Space, Switch, InputNumber } from 'antd';
import { BaseButtons } from './index';
import type { ButtonConfig } from './types';

const Example: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [maxCount, setMaxCount] = useState(3);
    const [rowData, setRowData] = useState({
        id: 1,
        name: '测试数据',
        canEdit: true,
        canDelete: true,
        status: 'active',
    });

    const handleEdit = async (row: any) => {
        console.log('编辑操作', row);
        // 模拟异步操作
        await new Promise((resolve) => setTimeout(resolve, 1000));
    };

    const handleDelete = async (row: any) => {
        console.log('删除操作', row);
        await new Promise((resolve) => setTimeout(resolve, 1500));
    };

    const handleView = async (row: any) => {
        console.log('查看详情', row);
    };

    const handleExport = async (row: any) => {
        console.log('导出数据', row);
        await new Promise((resolve) => setTimeout(resolve, 800));
    };

    const handleApprove = async (row: any) => {
        console.log('审批操作', row);
        await new Promise((resolve) => setTimeout(resolve, 1200));
    };

    // 基础按钮配置
    const basicButtons: ButtonConfig[] = [
        {
            label: '编辑',
            props: { type: 'primary', size: 'small' },
            event: handleEdit,
            show: (row) => row.canEdit,
        },
        {
            label: '删除',
            props: { danger: true, size: 'small' },
            event: handleDelete,
            show: (row) => row.canDelete,
        },
        {
            label: '查看',
            props: { size: 'small' },
            event: handleView,
        },
    ];

    // 带气泡按钮的配置
    const advancedButtons: ButtonConfig[] = [
        ...basicButtons,
        {
            label: '更多操作',
            props: { size: 'small' },
            children: [
                {
                    label: '导出',
                    props: { type: 'text' },
                    event: handleExport,
                },
                {
                    label: '审批',
                    props: { type: 'text' },
                    event: handleApprove,
                    show: (row) => row.status === 'active',
                },
                {
                    label: '归档',
                    props: { type: 'text' },
                    event: (row) => console.log('归档', row),
                },
            ],
        },
        {
            label: '复制',
            props: { size: 'small' },
            event: (row) => console.log('复制', row),
        },
        {
            label: '移动',
            props: { size: 'small' },
            event: (row) => console.log('移动', row),
        },
    ];

    return (
        <div style={{ padding: 24 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card title="基础用法" size="small">
                    <BaseButtons btns={basicButtons} row={rowData} loading={loading} />
                </Card>

                <Card title="带气泡按钮的高级用法" size="small">
                    <BaseButtons
                        btns={advancedButtons}
                        row={rowData}
                        maxCount={maxCount}
                        loading={loading}
                    />
                </Card>

                <Card title="网格布局" size="small">
                    <BaseButtons
                        btns={basicButtons}
                        row={rowData}
                        buttonSpan={8}
                        layout={{
                            type: 'flex',
                            justify: 'start',
                            align: 'middle',
                            gutter: [16, 8],
                        }}
                    />
                </Card>

                <Card title="控制面板" size="small">
                    <Space>
                        <span>全局Loading:</span>
                        <Switch checked={loading} onChange={setLoading} />

                        <span>最大显示数量:</span>
                        <InputNumber
                            min={1}
                            max={10}
                            value={maxCount}
                            onChange={(value) => setMaxCount(value || 3)}
                        />

                        <span>可编辑:</span>
                        <Switch
                            checked={rowData.canEdit}
                            onChange={(checked) =>
                                setRowData((prev) => ({ ...prev, canEdit: checked }))
                            }
                        />

                        <span>可删除:</span>
                        <Switch
                            checked={rowData.canDelete}
                            onChange={(checked) =>
                                setRowData((prev) => ({ ...prev, canDelete: checked }))
                            }
                        />
                    </Space>
                </Card>

                <Card title="函数式配置" size="small">
                    <BaseButtons
                        btns={(row) => [
                            {
                                label: `编辑 ${row.name}`,
                                props: { type: 'primary', size: 'small' },
                                event: handleEdit,
                            },
                            {
                                label: '动态按钮',
                                props: { size: 'small' },
                                event: (row) => console.log('动态按钮点击', row),
                                show: row.status === 'active',
                            },
                        ]}
                        row={rowData}
                    />
                </Card>
            </Space>
        </div>
    );
};

export default Example;
