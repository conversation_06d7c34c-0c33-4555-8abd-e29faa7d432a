.BaseButtons {
  height: 100%;
}

.inRow {
  width: 100%;
  text-align: left;
}

.btnMl12 {
  & + .btnMl12 {
    margin-top: 0;

    &:has(.inRow) {
      margin-top: 7px;
    }
  }

  &:not(:last-child) {
    margin-right: 12px;
  }
}

.ellipsisButton {
  display: flex;
  width: 10px;

  :global(.custom-button) {
    position: relative;
  }
}

.ellipsisButtonDot {
  width: 3px;
  height: 3px;
  background-color: #666666;
  border-radius: 50%;
  position: absolute;
  top: calc(50% - 1.5px + 0.05em);

  &::after,
  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 3px;
    background-color: #666666;
    border-radius: 50%;
    position: absolute;
    left: 0;
  }

  &::after {
    top: 0.4em;
  }

  &::before {
    top: -0.4em;
  }
}

.customButton {
  &Text {
    padding: 0;
  }
}
