import React from 'react';
import { Popover } from './deps';
import CustomButton from './CustomButton';
import { PopoverButtonProps } from './types';
import styles from './BaseButtons.less';

const PopoverButton: React.FC<PopoverButtonProps> = ({
    btn,
    row = {},
    loading = false,
    popoverProps = {},
    className = '',
    children,
    ...restProps
}) => {
    // 如果有子按钮，渲染为气泡按钮组
    if (btn.children && btn.children.length > 0) {
        const content = (
            <div>
                {btn.children.map((childBtn, index) => (
                    <div key={index} className="btn-ml12">
                        <PopoverButton
                            btn={childBtn}
                            row={row}
                            loading={loading}
                            className="in-row"
                        />
                    </div>
                ))}
            </div>
        );

        return (
            <Popover {...popoverProps} content={content} trigger="click">
                <CustomButton
                    btn={btn}
                    row={row}
                    pLoading={loading}
                    className={className}
                    {...restProps}
                >
                    {children}
                </CustomButton>
            </Popover>
        );
    }

    // 普通按钮
    return (
        <CustomButton btn={btn} row={row} pLoading={loading} className={className} {...restProps}>
            {children}
        </CustomButton>
    );
};

export default PopoverButton;
