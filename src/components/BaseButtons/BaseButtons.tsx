import React, { useMemo, useEffect, useState } from 'react';
import { Row, Col } from './deps';
import { noBorderButtonProp } from './deps';
import PopoverButton from './PopoverButton';
import { BaseButtonsProps, ButtonConfig } from './types';
import styles from './BaseButtons.less';

/**
 * @name BaseButtons
 * @description 按钮组组件，用于展示一组按钮的组件，主要亮点：
 * 1. 通过配置项进行渲染，见字段 <btns>
 * 2. 支持 antd Button 组件的所有属性，以及 Row 组件的布局配置，见字段 <layout>
 * 3. 支持最大显示按钮数限制，超过则通过气泡按钮展示
 * 4. 支持umi3.5+antd框架
 */
const BaseButtons: React.FC<BaseButtonsProps> = ({
    btns,
    row = {},
    layout = {
        type: 'flex',
        justify: 'start',
        align: 'bottom',
    },
    buttonSpan,
    maxCount = 5,
    loading = false,
    popoverProps = {},
}) => {
    const [computedBtns, setComputedBtns] = useState<ButtonConfig[]>([]);

    // 计算按钮配置
    const calculateButtons = () => {
        const btnList = typeof btns === 'function' ? btns(row) : btns;

        const processedBtns = (btnList || [])
            .map((item) => ({
                ...item,
                props: {
                    loading: false,
                    ...(item?.props || {}),
                },
                style: {
                    ...(item?.style || {}),
                },
            }))
            .filter((btn) => {
                if (!Object.prototype.hasOwnProperty.call(btn, 'show')) {
                    return true;
                }
                return typeof btn.show === 'function' ? btn.show(row) : !!btn.show;
            });

        setComputedBtns(processedBtns);
    };

    // 监听依赖变化
    useEffect(() => {
        calculateButtons();
    }, [btns, row]);

    // 计算可见按钮和气泡按钮
    const visibleButtons = useMemo(() => {
        return computedBtns?.slice(0, maxCount) || [];
    }, [computedBtns, maxCount]);

    const popoverButtons = useMemo(() => {
        return computedBtns?.slice(maxCount) || [];
    }, [computedBtns, maxCount]);

    // 按钮容器组件
    const BtnContainer = buttonSpan ? Col : 'div';

    return (
        <div className={styles.BaseButtons}>
            <Row {...layout} style={{ flexWrap: 'wrap', height: '100%' }}>
                {visibleButtons.map((btn, index) => (
                    <BtnContainer
                        key={index}
                        span={buttonSpan}
                        className={!buttonSpan ? styles.btnMl12 : ''}
                    >
                        <PopoverButton
                            loading={loading}
                            btn={btn}
                            row={row}
                            popoverProps={popoverProps}
                        />
                    </BtnContainer>
                ))}

                {popoverButtons.length > 0 && (
                    <BtnContainer>
                        <PopoverButton
                            loading={loading}
                            btn={{
                                props: {
                                    ...noBorderButtonProp,
                                },
                                children: popoverButtons,
                            }}
                            popoverProps={popoverProps}
                            row={row}
                            className={styles.ellipsisButton}
                        >
                            <span>&nbsp;</span>
                            <div className={styles.ellipsisButtonDot} />
                        </PopoverButton>
                    </BtnContainer>
                )}
            </Row>
        </div>
    );
};

export default BaseButtons;
